/**
 * Image Popup CSS
 * Styles for the image popup/modal component
 */

.image-popup-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-popup-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
}

.image-popup-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    animation: imagePopupZoomIn 0.3s ease;
}

@keyframes imagePopupZoomIn {
    from {
        transform: scale(0.5);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.image-popup-img {
    width: 100%;
    height: auto;
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.image-popup-close {
    position: absolute;
    top: -50px;
    right: 0;
    color: white;
    font-size: 35px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
    z-index: 10001;
    background: rgba(0, 0, 0, 0.5);
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.image-popup-close:hover {
    color: #007bff;
    background: rgba(0, 0, 0, 0.8);
}

.image-popup-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    font-size: 30px;
    font-weight: bold;
    cursor: pointer;
    user-select: none;
    transition: all 0.3s ease;
    background: rgba(0, 0, 0, 0.5);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10001;
}

.image-popup-nav:hover {
    color: #007bff;
    background: rgba(0, 0, 0, 0.8);
    transform: translateY(-50%) scale(1.1);
}

.image-popup-prev {
    left: -70px;
}

.image-popup-next {
    right: -70px;
}

.image-popup-caption {
    color: white;
    text-align: center;
    margin-top: 15px;
    font-size: 1.1rem;
    background: rgba(0, 0, 0, 0.7);
    padding: 12px 20px;
    border-radius: 25px;
    max-width: 100%;
    word-wrap: break-word;
}

.image-popup-counter {
    position: absolute;
    top: -50px;
    left: 0;
    color: white;
    background: rgba(0, 0, 0, 0.7);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    display: none;
}

.image-popup-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
    align-items: center;
    justify-content: center;
}

.image-popup-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: imagePopupSpin 1s linear infinite;
}

@keyframes imagePopupSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mobile responsive */
@media (max-width: 768px) {
    .image-popup-content {
        max-width: 95%;
        max-height: 95%;
    }
    
    .image-popup-img {
        max-height: 70vh;
    }
    
    .image-popup-close {
        top: -40px;
        font-size: 28px;
        width: 40px;
        height: 40px;
    }
    
    .image-popup-nav {
        font-size: 24px;
        width: 45px;
        height: 45px;
    }
    
    .image-popup-prev {
        left: -60px;
    }
    
    .image-popup-next {
        right: -60px;
    }
    
    .image-popup-caption {
        font-size: 1rem;
        padding: 10px 15px;
        margin-top: 10px;
    }
    
    .image-popup-counter {
        top: -40px;
        font-size: 0.8rem;
        padding: 6px 12px;
    }
}

@media (max-width: 480px) {
    .image-popup-content {
        max-width: 98%;
        max-height: 98%;
    }
    
    .image-popup-img {
        max-height: 60vh;
    }
    
    .image-popup-close {
        top: -35px;
        font-size: 24px;
        width: 35px;
        height: 35px;
    }
    
    .image-popup-nav {
        font-size: 20px;
        width: 40px;
        height: 40px;
    }
    
    .image-popup-prev {
        left: -50px;
    }
    
    .image-popup-next {
        right: -50px;
    }
    
    .image-popup-caption {
        font-size: 0.9rem;
        padding: 8px 12px;
    }
}

/* Touch device improvements */
@media (hover: none) and (pointer: coarse) {
    .image-popup-nav,
    .image-popup-close {
        background: rgba(0, 0, 0, 0.8);
    }
    
    .image-popup-nav:active,
    .image-popup-close:active {
        background: rgba(0, 123, 255, 0.8);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .image-popup-modal {
        background-color: rgba(0, 0, 0, 0.95);
    }
    
    .image-popup-close,
    .image-popup-nav,
    .image-popup-counter {
        background: rgba(0, 0, 0, 0.9);
        border: 2px solid white;
    }
    
    .image-popup-caption {
        background: rgba(0, 0, 0, 0.9);
        border: 1px solid white;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .image-popup-modal,
    .image-popup-content,
    .image-popup-nav,
    .image-popup-close {
        transition: none;
        animation: none;
    }
    
    .image-popup-spinner {
        animation: none;
        border: 4px solid white;
    }
}

/* Print styles */
@media print {
    .image-popup-modal {
        display: none !important;
    }
}
