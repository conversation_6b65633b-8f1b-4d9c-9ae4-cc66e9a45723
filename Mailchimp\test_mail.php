<?php
/**
 * Test file for Mailchimp Transactional Email functionality
 * 
 * This file allows you to test the email sending functionality
 * Replace the test email addresses with real ones before running
 */

require_once 'enhanced_mail_sender.php';

// Test configuration
$TEST_EMAIL = '<EMAIL>'; // Replace with your actual email
$TEST_NAME = 'Anuwat Nua-on';

echo "<h1>Mailchimp Transactional Email Test</h1>\n";
echo "<p>API Key: " . substr(MailConfig::API_KEY, 0, 10) . "...</p>\n";

// Test 1: API Connection
echo "<h2>Test 1: API Connection</h2>\n";
$mailer = new EnhancedMailSender();
$connection_test = $mailer->testConnection();

if (isset($connection_test['error'])) {
    echo "<p style='color: red;'>❌ Connection failed: " . $connection_test['message'] . "</p>\n";
} else {
    echo "<p style='color: green;'>✅ Connection successful!</p>\n";
    if (isset($connection_test['data']->username)) {
        echo "<p>Username: " . $connection_test['data']->username . "</p>\n";
    }
    if (isset($connection_test['data']->reputation)) {
        echo "<p>Reputation: " . $connection_test['data']->reputation . "</p>\n";
    }
}

echo "<hr>\n";

// Test 2: Available Templates
echo "<h2>Test 2: Available Templates</h2>\n";
$templates = $mailer->getAvailableTemplates();
echo "<ul>\n";
foreach ($templates as $template) {
    echo "<li>$template</li>\n";
}
echo "</ul>\n";

echo "<hr>\n";

// Test 3: Send Welcome Email (uncomment to test)
echo "<h2>Test 3: Send Welcome Email</h2>\n";
echo "<p><strong>Note:</strong> Uncomment the code below and replace the email address to test sending.</p>\n";
echo "<pre>\n";
echo "// Uncomment to test:\n";
echo "// \$result = \$mailer->sendWelcomeEmail('$TEST_EMAIL', '$TEST_NAME');\n";
echo "// if (isset(\$result->error)) {\n";
echo "//     echo \"Error: \" . \$result->message;\n";
echo "// } else {\n";
echo "//     echo \"Welcome email sent successfully!\";\n";
echo "// }\n";
echo "</pre>\n";

/*
// Uncomment to test sending welcome email:
$result = $mailer->sendWelcomeEmail($TEST_EMAIL, $TEST_NAME);
if (isset($result->error)) {
    echo "<p style='color: red;'>❌ Error: " . $result->message . "</p>\n";
} else {
    echo "<p style='color: green;'>✅ Welcome email sent successfully!</p>\n";
    if (is_array($result) && isset($result[0]->_id)) {
        echo "<p>Message ID: " . $result[0]->_id . "</p>\n";
        echo "<p>Status: " . $result[0]->status . "</p>\n";
    }
}
*/

echo "<hr>\n";

// Test 4: Send Registration Confirmation (uncomment to test)
echo "<h2>Test 4: Send Registration Confirmation</h2>\n";
echo "<p><strong>Note:</strong> Uncomment the code below and replace the email address to test sending.</p>\n";
echo "<pre>\n";
echo "// Uncomment to test:\n";
echo "// \$result = \$mailer->sendRegistrationConfirmation(\n";
echo "//     '$TEST_EMAIL',\n";
echo "//     '$TEST_NAME',\n";
echo "//     'REG-2024-001',\n";
echo "//     'December 15-16, 2024',\n";
echo "//     'Bangkok Convention Center',\n";
echo "//     'Standard Registration'\n";
echo "// );\n";
echo "</pre>\n";

/*
// Uncomment to test sending registration confirmation:
$result = $mailer->sendRegistrationConfirmation(
    $TEST_EMAIL,
    $TEST_NAME,
    'REG-2024-001',
    'December 15-16, 2024',
    'Bangkok Convention Center',
    'Standard Registration'
);
if (isset($result->error)) {
    echo "<p style='color: red;'>❌ Error: " . $result->message . "</p>\n";
} else {
    echo "<p style='color: green;'>✅ Registration confirmation sent successfully!</p>\n";
}
*/

echo "<hr>\n";

// Test 5: Send Conference Reminder (uncomment to test)
echo "<h2>Test 5: Send Conference Reminder</h2>\n";
echo "<p><strong>Note:</strong> Uncomment the code below and replace the email address to test sending.</p>\n";
echo "<pre>\n";
echo "// Uncomment to test:\n";
echo "// \$result = \$mailer->sendConferenceReminder(\n";
echo "//     '$TEST_EMAIL',\n";
echo "//     '$TEST_NAME',\n";
echo "//     7, // 7 days left\n";
echo "//     'December 15-16, 2024',\n";
echo "//     'Bangkok Convention Center',\n";
echo "//     '8:00 AM'\n";
echo "// );\n";
echo "</pre>\n";

/*
// Uncomment to test sending conference reminder:
$result = $mailer->sendConferenceReminder(
    $TEST_EMAIL,
    $TEST_NAME,
    7, // 7 days left
    'December 15-16, 2024',
    'Bangkok Convention Center',
    '8:00 AM'
);
if (isset($result->error)) {
    echo "<p style='color: red;'>❌ Error: " . $result->message . "</p>\n";
} else {
    echo "<p style='color: green;'>✅ Conference reminder sent successfully!</p>\n";
}
*/

echo "<hr>\n";

// Test 6: Custom Email
echo "<h2>Test 6: Custom Email</h2>\n";
echo "<p><strong>Note:</strong> Uncomment the code below and replace the email address to test sending.</p>\n";
echo "<pre>\n";
echo "// Uncomment to test:\n";
echo "// \$result = \$mailer->sendEmail(\n";
echo "//     '$TEST_EMAIL',\n";
echo "//     '$TEST_NAME',\n";
echo "//     MailConfig::DEFAULT_FROM_EMAIL,\n";
echo "//     MailConfig::DEFAULT_FROM_NAME,\n";
echo "//     'Test Email from CDIC Conference System',\n";
echo "//     '<h1>Test Email</h1><p>This is a test email from the CDIC Conference system.</p>',\n";
echo "//     'Test Email - This is a test email from the CDIC Conference system.'\n";
echo "// );\n";
echo "</pre>\n";

/*
// Uncomment to test sending custom email:
$result = $mailer->sendEmail(
    $TEST_EMAIL,
    $TEST_NAME,
    MailConfig::DEFAULT_FROM_EMAIL,
    MailConfig::DEFAULT_FROM_NAME,
    'Test Email from CDIC Conference System',
    '<h1>Test Email</h1><p>This is a test email from the CDIC Conference system.</p>',
    'Test Email - This is a test email from the CDIC Conference system.'
);
if (isset($result->error)) {
    echo "<p style='color: red;'>❌ Error: " . $result->message . "</p>\n";
} else {
    echo "<p style='color: green;'>✅ Custom email sent successfully!</p>\n";
}
*/

echo "<hr>\n";

echo "<h2>Instructions</h2>\n";
echo "<ol>\n";
echo "<li>Replace <code>\$TEST_EMAIL</code> with your actual email address</li>\n";
echo "<li>Uncomment the test sections you want to run</li>\n";
echo "<li>Run this file in your browser or via command line</li>\n";
echo "<li>Check your email inbox for the test messages</li>\n";
echo "</ol>\n";

echo "<h2>Usage Examples</h2>\n";
echo "<h3>Basic Usage:</h3>\n";
echo "<pre>\n";
echo "require_once 'enhanced_mail_sender.php';\n";
echo "\n";
echo "// Send welcome email\n";
echo "\$result = sendQuickWelcomeEmail('<EMAIL>', 'John Doe');\n";
echo "\n";
echo "// Send registration confirmation\n";
echo "\$result = sendQuickRegistrationConfirmation(\n";
echo "    '<EMAIL>',\n";
echo "    'John Doe',\n";
echo "    'REG-2024-001'\n";
echo ");\n";
echo "\n";
echo "// Send reminder\n";
echo "\$result = sendQuickReminder('<EMAIL>', 'John Doe', 7);\n";
echo "</pre>\n";

echo "<h3>Advanced Usage:</h3>\n";
echo "<pre>\n";
echo "\$mailer = new EnhancedMailSender();\n";
echo "\n";
echo "// Send custom template email\n";
echo "\$variables = ['NAME' => 'John Doe', 'CUSTOM_VAR' => 'Custom Value'];\n";
echo "\$result = \$mailer->sendTemplateEmail('welcome', '<EMAIL>', 'John Doe', \$variables);\n";
echo "\n";
echo "// Send bulk emails\n";
echo "\$recipients = [\n";
echo "    ['email' => '<EMAIL>', 'name' => 'User 1', 'variables' => ['NAME' => 'User 1']],\n";
echo "    ['email' => '<EMAIL>', 'name' => 'User 2', 'variables' => ['NAME' => 'User 2']]\n";
echo "];\n";
echo "\$results = \$mailer->sendBulkTemplateEmails('welcome', \$recipients);\n";
echo "</pre>\n";
?>
