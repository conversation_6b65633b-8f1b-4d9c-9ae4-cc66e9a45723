<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto Popup Image - CDIC Conference</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .main-content {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
        }

        .trigger-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            transition: background-color 0.3s;
        }

        .trigger-button:hover {
            background-color: #0056b3;
        }

        /* Popup Overlay */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            animation: fadeIn 0.3s ease-in-out;
        }

        .popup-overlay.show {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* Popup Content */
        .popup-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            animation: slideIn 0.3s ease-in-out;
        }

        .popup-image {
            max-width: 100%;
            max-height: 70vh;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .popup-title {
            text-align: center;
            margin-bottom: 15px;
            color: #333;
            font-size: 18px;
            font-weight: bold;
        }

        /* Close Button */
        .close-button {
            position: absolute;
            top: 10px;
            right: 15px;
            background: #ff4757;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s;
        }

        .close-button:hover {
            background: #ff3742;
        }

        /* Animations */
        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: scale(0.8) translateY(-50px);
            }

            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* Auto popup notification */
        .auto-popup-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            color: #1976d2;
        }

        .countdown {
            font-weight: bold;
            color: #ff4757;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .popup-content {
                margin: 20px;
                padding: 15px;
            }

            .popup-image {
                max-height: 60vh;
            }
        }
    </style>
</head>

<body>
    <div class="main-content">
        <h1>Auto Popup Image Display</h1>
        <p>This page will automatically display the Queen Sirikit image in a popup.</p>

        <div class="auto-popup-info">
            <strong>Auto Popup:</strong> The image will appear automatically in <span id="countdown"
                class="countdown">3</span> seconds.
        </div>

        <div>
            <button class="trigger-button" onclick="showPopup()">Show Image Now</button>
            <button class="trigger-button" onclick="toggleAutoPopup()">
                <span id="auto-toggle-text">Disable Auto Popup</span>
            </button>
        </div>

        <div style="margin-top: 30px;">
            <h3>Image Information:</h3>
            <p><strong>Image URL:</strong> http://localhost/cdicconference.com/assets/images/QueenSirikit.png</p>
            <p><strong>Auto Popup:</strong> <span id="auto-status">Enabled</span></p>
        </div>
    </div>

    <!-- Popup Overlay -->
    <div id="popup-overlay" class="popup-overlay" onclick="closePopup(event)">
        <div class="popup-content" onclick="event.stopPropagation()">
            <button class="close-button" onclick="closePopup()">&times;</button>
            <div class="popup-title">Queen Sirikit Image</div>
            <img id="popup-image" class="popup-image"
                src="http://localhost/cdicconference.com/assets/images/QueenSirikit.png" alt="Queen Sirikit"
                onerror="handleImageError()">
        </div>
    </div>

    <script>
        let autoPopupEnabled = true;
        let countdownTimer;
        let autoPopupTimer;

        // Auto popup functionality
        function startAutoPopup() {
            if (!autoPopupEnabled) return;

            let countdown = 3;
            const countdownElement = document.getElementById('countdown');

            // Update countdown display
            countdownTimer = setInterval(() => {
                countdown--;
                countdownElement.textContent = countdown;

                if (countdown <= 0) {
                    clearInterval(countdownTimer);
                    showPopup();
                }
            }, 1000);
        }

        // Show popup function
        function showPopup() {
            const overlay = document.getElementById('popup-overlay');
            overlay.classList.add('show');

            // Clear any existing timers
            if (countdownTimer) clearInterval(countdownTimer);
            if (autoPopupTimer) clearTimeout(autoPopupTimer);
        }

        // Close popup function
        function closePopup(event) {
            if (event && event.target !== event.currentTarget && !event.target.classList.contains('close-button')) {
                return;
            }

            const overlay = document.getElementById('popup-overlay');
            overlay.classList.remove('show');

            // Restart auto popup after closing (if enabled)
            if (autoPopupEnabled) {
                setTimeout(() => {
                    document.getElementById('countdown').textContent = '3';
                    startAutoPopup();
                }, 2000); // Wait 2 seconds before starting countdown again
            }
        }

        // Toggle auto popup
        function toggleAutoPopup() {
            autoPopupEnabled = !autoPopupEnabled;
            const toggleText = document.getElementById('auto-toggle-text');
            const autoStatus = document.getElementById('auto-status');

            if (autoPopupEnabled) {
                toggleText.textContent = 'Disable Auto Popup';
                autoStatus.textContent = 'Enabled';
                autoStatus.style.color = '#4caf50';

                // Restart countdown
                document.getElementById('countdown').textContent = '3';
                startAutoPopup();
            } else {
                toggleText.textContent = 'Enable Auto Popup';
                autoStatus.textContent = 'Disabled';
                autoStatus.style.color = '#ff4757';

                // Clear timers
                if (countdownTimer) clearInterval(countdownTimer);
                if (autoPopupTimer) clearTimeout(autoPopupTimer);

                document.getElementById('countdown').textContent = '∞';
            }
        }

        // Handle image loading error
        function handleImageError() {
            const img = document.getElementById('popup-image');
            img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4=';
            img.alt = 'Image not found - Please check the URL';

            // Update popup title
            document.querySelector('.popup-title').textContent = 'Image Not Found';
            document.querySelector('.popup-title').style.color = '#ff4757';
        }

        // Keyboard event handling
        document.addEventListener('keydown', function (event) {
            if (event.key === 'Escape') {
                closePopup();
            } else if (event.key === 'Enter' || event.key === ' ') {
                if (!document.getElementById('popup-overlay').classList.contains('show')) {
                    showPopup();
                }
            }
        });

        // Start auto popup when page loads
        window.addEventListener('load', function () {
            setTimeout(startAutoPopup, 500); // Small delay to let page settle
        });

        // Prevent right-click on image (optional)
        document.getElementById('popup-image').addEventListener('contextmenu', function (e) {
            e.preventDefault();
        });
    </script>
</body>

</html>