<?php
/**
 * Example usage of the MailSender class
 * 
 * This file demonstrates how to use the Mailchimp Transactional email functionality
 */

require_once 'send_mail.php';

// Example 1: Send a simple email
function example1_simple_email() {
    echo "<h2>Example 1: Simple Email</h2>\n";
    
    $mailer = new MailSender();
    
    $result = $mailer->sendEmail(
        '<EMAIL>',                    // To email
        'Anuwat Nua-on',                           // To name
        '<EMAIL>',          // From email
        'CDIC Conference System',              // From name
        'Test Email from CDIC Conference',     // Subject
        '<h1>Hello!</h1><p>This is a <strong>test email</strong> from CDIC Conference system.</p><p>If you received this, the email system is working correctly!</p>', // HTML content
        'Hello! This is a test email from CDIC Conference system. If you received this, the email system is working correctly!' // Text content
    );
    
    if (isset($result->error)) {
        echo "Error: " . $result->message . "\n";
    } else {
        echo "Email sent successfully!\n";
        echo "Message ID: " . (isset($result[0]->_id) ? $result[0]->_id : 'N/A') . "\n";
        echo "Status: " . (isset($result[0]->status) ? $result[0]->status : 'N/A') . "\n";
    }
    
    return $result;
}

// Example 2: Send email with attachments
function example2_email_with_attachments() {
    echo "<h2>Example 2: Email with Attachments</h2>\n";
    
    $mailer = new MailSender();
    
    // Create a sample attachment (base64 encoded content)
    $attachments = [
        [
            'type' => 'text/plain',
            'name' => 'sample.txt',
            'content' => base64_encode('This is a sample text file attachment.')
        ]
    ];
    
    $result = $mailer->sendEmailWithAttachments(
        '<EMAIL>',                    // To email
        'Test User',                           // To name
        '<EMAIL>',          // From email
        'CDIC Conference System',              // From name
        'Email with Attachment',               // Subject
        '<h1>Email with Attachment</h1><p>This email contains an attachment.</p>', // HTML content
        $attachments,                          // Attachments array
        'Email with Attachment - This email contains an attachment.' // Text content
    );
    
    if (isset($result->error)) {
        echo "Error: " . $result->message . "\n";
    } else {
        echo "Email with attachment sent successfully!\n";
        echo "Message ID: " . (isset($result[0]->_id) ? $result[0]->_id : 'N/A') . "\n";
    }
    
    return $result;
}

// Example 3: Send bulk email to multiple recipients
function example3_bulk_email() {
    echo "<h2>Example 3: Bulk Email</h2>\n";
    
    $mailer = new MailSender();
    
    $recipients = [
        ['email' => '<EMAIL>', 'name' => 'User One'],
        ['email' => '<EMAIL>', 'name' => 'User Two'],
        ['email' => '<EMAIL>', 'name' => 'User Three']
    ];
    
    $html_content = '
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { background-color: #007bff; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background-color: #f8f9fa; }
            .footer { padding: 10px; text-align: center; font-size: 12px; color: #666; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>CDIC Conference 2024</h1>
        </div>
        <div class="content">
            <h2>Important Conference Update</h2>
            <p>Dear Conference Participants,</p>
            <p>We have an important update regarding the upcoming CDIC Conference 2024.</p>
            <ul>
                <li>New speakers have been added to the agenda</li>
                <li>Workshop schedules have been updated</li>
                <li>Registration deadline extended</li>
            </ul>
            <p>Please visit our website for the latest information.</p>
            <p>Best regards,<br>CDIC Conference Organizing Committee</p>
        </div>
        <div class="footer">
            <p>&copy; 2024 CDIC Conference. All rights reserved.</p>
        </div>
    </body>
    </html>';
    
    $text_content = "CDIC Conference 2024\n\nDear Conference Participants,\n\nWe have an important update regarding the upcoming CDIC Conference 2024.\n\n- New speakers have been added to the agenda\n- Workshop schedules have been updated\n- Registration deadline extended\n\nPlease visit our website for the latest information.\n\nBest regards,\nCDIC Conference Organizing Committee\n\n© 2024 CDIC Conference. All rights reserved.";
    
    $result = $mailer->sendBulkEmail(
        $recipients,                           // Recipients array
        '<EMAIL>',             // From email
        'CDIC Conference',                     // From name
        'Important Conference Update',         // Subject
        $html_content,                         // HTML content
        $text_content                          // Text content
    );
    
    if (isset($result->error)) {
        echo "Error: " . $result->message . "\n";
    } else {
        echo "Bulk email sent successfully!\n";
        echo "Number of recipients: " . count($recipients) . "\n";
        if (is_array($result)) {
            echo "Messages sent: " . count($result) . "\n";
        }
    }
    
    return $result;
}

// Example 4: Get account information
function example4_account_info() {
    echo "<h2>Example 4: Account Information</h2>\n";
    
    $mailer = new MailSender();
    $result = $mailer->getAccountInfo();
    
    if (isset($result->error)) {
        echo "Error: " . $result->message . "\n";
    } else {
        echo "Account information retrieved successfully!\n";
        if (isset($result->username)) {
            echo "Username: " . $result->username . "\n";
        }
        if (isset($result->created_at)) {
            echo "Account created: " . $result->created_at . "\n";
        }
        if (isset($result->reputation)) {
            echo "Reputation: " . $result->reputation . "\n";
        }
    }
    
    return $result;
}

// Example 5: Send welcome email (using the helper function)
function example5_welcome_email() {
    echo "<h2>Example 5: Welcome Email</h2>\n";
    
    $result = sendWelcomeEmail('<EMAIL>', 'New User');
    
    if (isset($result->error)) {
        echo "Error: " . $result->message . "\n";
    } else {
        echo "Welcome email sent successfully!\n";
        echo "Message ID: " . (isset($result[0]->_id) ? $result[0]->_id : 'N/A') . "\n";
    }
    
    return $result;
}

// Main execution
if (basename(__FILE__) == basename($_SERVER["SCRIPT_FILENAME"])) {
    echo "<h1>Mailchimp Transactional Email Examples</h1>\n";
    echo "<p>This file demonstrates various ways to use the MailSender class.</p>\n";
    echo "<p><strong>Note:</strong> These examples use test email addresses. Replace with real addresses to send actual emails.</p>\n";
    
    // Uncomment the examples you want to run:
    
    // example1_simple_email();
    // echo "<hr>\n";
    
    // example2_email_with_attachments();
    // echo "<hr>\n";
    
    // example3_bulk_email();
    // echo "<hr>\n";
    
    // example4_account_info();
    // echo "<hr>\n";
    
    // example5_welcome_email();
    // echo "<hr>\n";
    
    echo "<p>To run examples, uncomment the function calls above.</p>\n";
    echo "<p>Make sure to replace test email addresses with real ones before sending.</p>\n";
}
?>
