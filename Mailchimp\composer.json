{"name": "mailchimp/transactional", "version": "1.0.59", "description": "", "keywords": ["swagger", "php", "sdk", "api"], "homepage": "http://swagger.io", "license": "proprietary", "authors": [{"name": "Mailchimp", "homepage": "https://github.com/mailchimp/mailchimp-transactional-php"}], "require": {"php": ">=7.2", "ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "guzzlehttp/guzzle": "^6.4 || ^7.2"}, "require-dev": {"phpunit/phpunit": "^7", "squizlabs/php_codesniffer": "~2.6", "friendsofphp/php-cs-fixer": "~2.12"}, "autoload": {"psr-4": {"MailchimpTransactional\\": "lib/"}}, "autoload-dev": {"psr-4": {"MailchimpTransactional\\": "test/"}}}