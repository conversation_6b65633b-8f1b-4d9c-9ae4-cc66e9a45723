<?PHP
// header('Access-Control-Allow-Origin: *');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">

    <link rel="apple-touch-icon" sizes="57x57" href="<?php echo _PathURL;?>/assets/images/apple-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?php echo _PathURL;?>/assets/images/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?php echo _PathURL;?>/assets/images/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?php echo _PathURL;?>/assets/images/apple-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?php echo _PathURL;?>/assets/images/apple-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?php echo _PathURL;?>/assets/images/apple-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?php echo _PathURL;?>/assets/images/apple-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?php echo _PathURL;?>/assets/images/apple-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo _PathURL;?>/assets/images/apple-icon-180x180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="<?php echo _PathURL;?>/assets/images/android-icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo _PathURL;?>/assets/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="<?php echo _PathURL;?>/assets/images/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo _PathURL;?>/assets/images/favicon-16x16.png">
    <link rel="manifest" href="<?php echo _PathURL;?>/manifest.json">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-TileImage" content="<?php echo _PathURL;?>/assets/images/ms-icon-144x144.png">
    <meta name="theme-color" content="#ffffff">
    <title><?php echo _PageTitle;?> > Cyber Defense Initiative Conference - CDIC2025 Thailand Cybersecurity Day </title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta name="keywords" content="CDIC, CDIC2025, CDICConference, ACIS Professional Center, Cyber Defense Initiative Conference, Thailand Cybersecurity Day">
    <meta name="description" content="CDIC2025 - งานสัมมนาประจำปีด้านความมั่นคงปลอดภัยไซเบอร์ที่ยิ่งใหญ่ที่สุดในประเทศไทยและภูมิภาคอาเซียน Cyber Defense Initiative Conference - Powering Techno-drive in Digi-hype Behaviour - Thailand Cybersecurity Day">


    <!-- Cookie Alert -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Wruczek/Bootstrap-Cookie-Alert@gh-pages/cookiealert.css">

    <!-- Favicon -->
    <link href="<?php echo _PathURL;?>/assets/images/favicon.ico?x" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Prompt:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,100;1,200;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet"> 

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="<?php echo _PathURL;?>/lib/animate/animate.min.css" rel="stylesheet">
    <link href="<?php echo _PathURL;?>/lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">
    <link href="<?php echo _PathURL;?>/lib/lightbox/css/lightbox.min.css" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="<?php echo _PathURL;?>/assets/css/bootstrap.min.css?v2" rel="stylesheet">
    <!-- <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.0/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-gH2yIJqKdNHPEq0n4Mqa/HGKIhSkIHeL5AyhkYV8i59U5AR6csBvApHHNl/vI1Bx" crossorigin="anonymous"> -->

    
    <!-- Template Stylesheet -->
    <link href="<?php echo _PathURL;?>/assets/css/style.css?v1" rel="stylesheet">
    <link href="<?php echo _PathURL;?>/assets/css/style2025.css?v1" rel="stylesheet">
    <style>
        /* body {
            -webkit-filter: grayscale(70%);
            filter: grayscale(70%);
        } */
        .btn-primary{
            color:#2196F3 ;
            background-color:#2196F3 ;
            border-color:#2196F3 ;
        }
        .bg-primary{
            background-color:#80b6c5 !important;
        }
        .btn-link{
            color:#80b6c5;
        }
        .btn-link:hover {
            color: #fff;
        }

        body, h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6{
            font-family: 'Prompt', sans-serif;
        }
        body{
            color:#5d5d5d;
        }
        /* .bg-dark{
            background-color:#0e2658 !important;
        } */
        a {
            color: #008bd1;
        }
        a:hover {
            color: #cc343e;
        }
        .btn.btn-primary:hover, .btn.btn-outline-primary:hover{
            background-color:#008bd1;
            border-color:#008bd1;
        }
    </style>

    <style>
        .main-content {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
        }

        .trigger-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            transition: background-color 0.3s;
        }

        .trigger-button:hover {
            background-color: #0056b3;
        }

        /* Popup Overlay */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            animation: fadeIn 0.3s ease-in-out;
        }

        .popup-overlay.show {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* Popup Content */
        .popup-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            animation: slideIn 0.3s ease-in-out;
        }

        .popup-image {
            max-width: 100%;
            max-height: 70vh;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .popup-title {
            text-align: center;
            margin-bottom: 15px;
            color: #333;
            font-size: 18px;
            font-weight: bold;
        }

        /* Close Button */
        .close-button {
            position: absolute;
            top: 10px;
            right: 15px;
            background: #ff4757;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s;
        }

        .close-button:hover {
            background: #ff3742;
        }

        /* Animations */
        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: scale(0.8) translateY(-50px);
            }

            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* Auto popup notification */
        .auto-popup-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            color: #1976d2;
        }

        .countdown {
            font-weight: bold;
            color: #ff4757;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .popup-content {
                margin: 20px;
                padding: 15px;
            }

            .popup-image {
                max-height: 60vh;
            }
        }
    </style>
</head>