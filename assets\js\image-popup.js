/**
 * Image Popup Component
 * Easy-to-use image popup/modal system for CDIC Conference website
 * 
 * Usage:
 * 1. Include this JS file in your page
 * 2. Include the CSS file (image-popup.css)
 * 3. Add onclick="ImagePopup.open('image-url', 'caption')" to any image
 */

class ImagePopup {
    constructor() {
        this.modal = null;
        this.currentImages = [];
        this.currentIndex = 0;
        this.init();
    }

    init() {
        // Create modal HTML if it doesn't exist
        if (!document.getElementById('imagePopupModal')) {
            this.createModal();
        }
        
        // Bind keyboard events
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));
    }

    createModal() {
        const modalHTML = `
            <div id="imagePopupModal" class="image-popup-modal">
                <div class="image-popup-content">
                    <span class="image-popup-close">&times;</span>
                    <span class="image-popup-nav image-popup-prev">&#10094;</span>
                    <span class="image-popup-nav image-popup-next">&#10095;</span>
                    <img id="imagePopupImg" class="image-popup-img" src="" alt="Popup Image">
                    <div id="imagePopupCaption" class="image-popup-caption"></div>
                    <div class="image-popup-counter">
                        <span id="imagePopupCounter"></span>
                    </div>
                </div>
                <div class="image-popup-loading">
                    <div class="image-popup-spinner"></div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.modal = document.getElementById('imagePopupModal');
        
        // Bind events
        this.bindEvents();
    }

    bindEvents() {
        // Close modal when clicking outside
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.close();
            }
        });

        // Close button
        document.querySelector('.image-popup-close').addEventListener('click', () => this.close());

        // Navigation arrows
        document.querySelector('.image-popup-prev').addEventListener('click', () => this.previous());
        document.querySelector('.image-popup-next').addEventListener('click', () => this.next());

        // Prevent image click from closing modal
        document.getElementById('imagePopupImg').addEventListener('click', (e) => e.stopPropagation());
    }

    open(imageSrc, caption = '', imageArray = null) {
        // Show loading
        this.showLoading();
        
        // If imageArray is provided, use it for navigation
        if (imageArray && Array.isArray(imageArray)) {
            this.currentImages = imageArray;
            this.currentIndex = imageArray.findIndex(img => 
                (typeof img === 'string' ? img : img.src) === imageSrc
            );
        } else {
            // Single image
            this.currentImages = [{
                src: imageSrc,
                caption: caption
            }];
            this.currentIndex = 0;
        }

        if (this.currentIndex === -1) this.currentIndex = 0;

        this.showCurrentImage();
        this.modal.classList.add('show');
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }

    showCurrentImage() {
        const currentImage = this.currentImages[this.currentIndex];
        const imgSrc = typeof currentImage === 'string' ? currentImage : currentImage.src;
        const imgCaption = typeof currentImage === 'string' ? '' : (currentImage.caption || '');

        const img = document.getElementById('imagePopupImg');
        const caption = document.getElementById('imagePopupCaption');
        const counter = document.getElementById('imagePopupCounter');

        // Show loading while image loads
        this.showLoading();

        img.onload = () => {
            this.hideLoading();
        };

        img.onerror = () => {
            this.hideLoading();
            img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4=';
        };

        img.src = imgSrc;
        caption.textContent = imgCaption;

        // Update counter
        if (this.currentImages.length > 1) {
            counter.textContent = `${this.currentIndex + 1} / ${this.currentImages.length}`;
            counter.style.display = 'block';
            document.querySelector('.image-popup-prev').style.display = 'block';
            document.querySelector('.image-popup-next').style.display = 'block';
        } else {
            counter.style.display = 'none';
            document.querySelector('.image-popup-prev').style.display = 'none';
            document.querySelector('.image-popup-next').style.display = 'none';
        }
    }

    close() {
        this.modal.classList.remove('show');
        document.body.style.overflow = ''; // Restore scrolling
    }

    next() {
        if (this.currentImages.length > 1) {
            this.currentIndex = (this.currentIndex + 1) % this.currentImages.length;
            this.showCurrentImage();
        }
    }

    previous() {
        if (this.currentImages.length > 1) {
            this.currentIndex = (this.currentIndex - 1 + this.currentImages.length) % this.currentImages.length;
            this.showCurrentImage();
        }
    }

    showLoading() {
        document.querySelector('.image-popup-loading').style.display = 'flex';
    }

    hideLoading() {
        document.querySelector('.image-popup-loading').style.display = 'none';
    }

    handleKeyboard(e) {
        if (this.modal && this.modal.classList.contains('show')) {
            switch(e.key) {
                case 'Escape':
                    this.close();
                    break;
                case 'ArrowLeft':
                    this.previous();
                    break;
                case 'ArrowRight':
                    this.next();
                    break;
            }
        }
    }

    // Static methods for easy access
    static open(imageSrc, caption = '', imageArray = null) {
        if (!window.imagePopupInstance) {
            window.imagePopupInstance = new ImagePopup();
        }
        window.imagePopupInstance.open(imageSrc, caption, imageArray);
    }

    static close() {
        if (window.imagePopupInstance) {
            window.imagePopupInstance.close();
        }
    }

    // Utility method to automatically add popup to all images with data-popup attribute
    static autoInit() {
        document.addEventListener('DOMContentLoaded', () => {
            const images = document.querySelectorAll('img[data-popup]');
            images.forEach(img => {
                img.style.cursor = 'pointer';
                img.addEventListener('click', () => {
                    const caption = img.getAttribute('data-popup') || img.alt || '';
                    ImagePopup.open(img.src, caption);
                });
            });
        });
    }

    // Utility method to create gallery from images with same data-gallery attribute
    static initGallery(galleryName) {
        document.addEventListener('DOMContentLoaded', () => {
            const images = document.querySelectorAll(`img[data-gallery="${galleryName}"]`);
            if (images.length === 0) return;

            const imageArray = Array.from(images).map(img => ({
                src: img.src,
                caption: img.getAttribute('data-caption') || img.alt || ''
            }));

            images.forEach((img, index) => {
                img.style.cursor = 'pointer';
                img.addEventListener('click', () => {
                    ImagePopup.open(img.src, imageArray[index].caption, imageArray);
                });
            });
        });
    }
}

// Auto-initialize when script loads
ImagePopup.autoInit();

// Make it globally available
window.ImagePopup = ImagePopup;
