<?php
/**
 * Enhanced Mailchimp Transactional Email Sender
 * 
 * This file provides enhanced functionality to send emails using Mailchimp Transactional API
 * with template support and configuration management
 */

require_once __DIR__ . '/lib/Configuration.php';
require_once __DIR__ . '/mail_config.php';

use MailchimpTransactional\Configuration;

class EnhancedMailSender {
    private $config;
    private $apiKey;
    
    public function __construct($apiKey = null) {
        $this->apiKey = $apiKey ?: MailConfig::API_KEY;
        $this->config = new Configuration();
        $this->config->setApiKey($this->apiKey);
    }
    
    /**
     * Send email using a predefined template
     * 
     * @param string $template_name Name of the template to use
     * @param string $to_email Recipient email address
     * @param string $to_name Recipient name
     * @param array $variables Variables to replace in the template
     * @param string $from_email Sender email address (optional)
     * @param string $from_name Sender name (optional)
     * @return mixed Response from Mailchimp API
     */
    public function sendTemplateEmail($template_name, $to_email, $to_name, $variables = [], $from_email = null, $from_name = null) {
        $template = MailConfig::getTemplate($template_name);
        if (!$template) {
            return [
                'error' => true,
                'message' => "Template '$template_name' not found"
            ];
        }
        
        $from_email = $from_email ?: MailConfig::DEFAULT_FROM_EMAIL;
        $from_name = $from_name ?: MailConfig::DEFAULT_FROM_NAME;
        
        // Replace placeholders in template
        $subject = MailConfig::replacePlaceholders($template['subject'], $variables);
        $html_content = MailConfig::replacePlaceholders($template['html'], $variables);
        $text_content = MailConfig::replacePlaceholders($template['text'], $variables);
        
        return $this->sendEmail($to_email, $to_name, $from_email, $from_name, $subject, $html_content, $text_content);
    }
    
    /**
     * Send a simple email with default configuration
     * 
     * @param string $to_email Recipient email address
     * @param string $to_name Recipient name
     * @param string $from_email Sender email address
     * @param string $from_name Sender name
     * @param string $subject Email subject
     * @param string $html_content HTML content of the email
     * @param string $text_content Plain text content of the email (optional)
     * @return mixed Response from Mailchimp API
     */
    public function sendEmail($to_email, $to_name, $from_email, $from_name, $subject, $html_content, $text_content = '') {
        $message = [
            'html' => $html_content,
            'text' => $text_content,
            'subject' => $subject,
            'from_email' => $from_email,
            'from_name' => $from_name,
            'to' => [
                [
                    'email' => $to_email,
                    'name' => $to_name,
                    'type' => 'to'
                ]
            ],
            'headers' => [
                'Reply-To' => MailConfig::DEFAULT_REPLY_TO
            ],
            'important' => false,
            'track_opens' => MailConfig::TRACK_OPENS,
            'track_clicks' => MailConfig::TRACK_CLICKS,
            'auto_text' => MailConfig::AUTO_TEXT,
            'auto_html' => MailConfig::AUTO_HTML,
            'inline_css' => MailConfig::INLINE_CSS,
            'url_strip_qs' => false,
            'preserve_recipients' => false,
            'view_content_link' => false,
            'tracking_domain' => null,
            'signing_domain' => null,
            'return_path_domain' => null
        ];
        
        $body = [
            'message' => $message,
            'async' => false,
            'ip_pool' => 'Main Pool'
        ];
        
        try {
            $response = $this->config->messages->send($body);
            return $response;
        } catch (Exception $e) {
            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Send welcome email using template
     * 
     * @param string $to_email Recipient email address
     * @param string $to_name Recipient name
     * @return mixed Response from Mailchimp API
     */
    public function sendWelcomeEmail($to_email, $to_name) {
        $variables = [
            'NAME' => $to_name,
            'UNSUBSCRIBE_URL' => 'https://cdicconference.com/unsubscribe?email=' . urlencode($to_email)
        ];
        
        return $this->sendTemplateEmail('welcome', $to_email, $to_name, $variables);
    }
    
    /**
     * Send registration confirmation email using template
     * 
     * @param string $to_email Recipient email address
     * @param string $to_name Recipient name
     * @param string $registration_id Registration ID
     * @param string $conference_date Conference date
     * @param string $venue Venue information
     * @param string $registration_type Type of registration
     * @return mixed Response from Mailchimp API
     */
    public function sendRegistrationConfirmation($to_email, $to_name, $registration_id, $conference_date, $venue, $registration_type) {
        $variables = [
            'NAME' => $to_name,
            'REGISTRATION_ID' => $registration_id,
            'CONFERENCE_DATE' => $conference_date,
            'VENUE' => $venue,
            'REGISTRATION_TYPE' => $registration_type
        ];
        
        return $this->sendTemplateEmail('registration_confirmation', $to_email, $to_name, $variables);
    }
    
    /**
     * Send conference reminder email using template
     * 
     * @param string $to_email Recipient email address
     * @param string $to_name Recipient name
     * @param int $days_left Days left until conference
     * @param string $conference_date Conference date
     * @param string $venue Venue information
     * @param string $checkin_time Check-in time
     * @return mixed Response from Mailchimp API
     */
    public function sendConferenceReminder($to_email, $to_name, $days_left, $conference_date, $venue, $checkin_time) {
        $variables = [
            'NAME' => $to_name,
            'DAYS_LEFT' => $days_left,
            'CONFERENCE_DATE' => $conference_date,
            'VENUE' => $venue,
            'CHECKIN_TIME' => $checkin_time
        ];
        
        return $this->sendTemplateEmail('reminder', $to_email, $to_name, $variables);
    }
    
    /**
     * Send bulk emails using template
     * 
     * @param string $template_name Name of the template to use
     * @param array $recipients Array of recipients with 'email', 'name', and 'variables' keys
     * @param string $from_email Sender email address (optional)
     * @param string $from_name Sender name (optional)
     * @return array Array of responses from Mailchimp API
     */
    public function sendBulkTemplateEmails($template_name, $recipients, $from_email = null, $from_name = null) {
        $results = [];
        
        foreach ($recipients as $recipient) {
            $variables = isset($recipient['variables']) ? $recipient['variables'] : [];
            $result = $this->sendTemplateEmail(
                $template_name,
                $recipient['email'],
                $recipient['name'],
                $variables,
                $from_email,
                $from_name
            );
            
            $results[] = [
                'email' => $recipient['email'],
                'result' => $result
            ];
        }
        
        return $results;
    }
    
    /**
     * Get available templates
     * 
     * @return array Array of available template names
     */
    public function getAvailableTemplates() {
        return MailConfig::getAvailableTemplates();
    }
    
    /**
     * Test API connection
     * 
     * @return mixed Response from Mailchimp API
     */
    public function testConnection() {
        try {
            $response = $this->config->users->info();
            return [
                'success' => true,
                'data' => $response
            ];
        } catch (Exception $e) {
            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }
}

// Helper functions for common email operations
function sendQuickWelcomeEmail($to_email, $to_name) {
    $mailer = new EnhancedMailSender();
    return $mailer->sendWelcomeEmail($to_email, $to_name);
}

function sendQuickRegistrationConfirmation($to_email, $to_name, $registration_id, $conference_date = 'TBD', $venue = 'TBD', $registration_type = 'Standard') {
    $mailer = new EnhancedMailSender();
    return $mailer->sendRegistrationConfirmation($to_email, $to_name, $registration_id, $conference_date, $venue, $registration_type);
}

function sendQuickReminder($to_email, $to_name, $days_left, $conference_date = 'TBD', $venue = 'TBD', $checkin_time = '8:00 AM') {
    $mailer = new EnhancedMailSender();
    return $mailer->sendConferenceReminder($to_email, $to_name, $days_left, $conference_date, $venue, $checkin_time);
}

// If this file is called directly, show available templates
if (basename(__FILE__) == basename($_SERVER["SCRIPT_FILENAME"])) {
    echo "Enhanced Mail Sender initialized successfully!\n\n";
    echo "Available templates:\n";
    $templates = MailConfig::getAvailableTemplates();
    foreach ($templates as $template) {
        echo "- $template\n";
    }
    echo "\nUse the EnhancedMailSender class or the helper functions to send emails.\n";
}
?>
