<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Popup Demo - CDIC Conference</title>
    
    <!-- Include the popup CSS -->
    <link rel="stylesheet" href="assets/css/image-popup.css">
    
    <!-- Bootstrap for demo styling (optional) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px 0;
        }
        
        .demo-image {
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-radius: 8px;
        }
        
        .demo-image:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .code-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .feature-box {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .btn-demo {
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center text-primary mb-4">
                    <i class="fas fa-images"></i> Image Popup Demo
                </h1>
                <p class="text-center lead">Click on any image to open it in a popup modal</p>
            </div>
        </div>

        <!-- Method 1: Simple onclick -->
        <div class="feature-box">
            <h3 class="text-primary">Method 1: Simple onclick</h3>
            <p>The easiest way to add popup functionality to any image:</p>
            
            <div class="row">
                <div class="col-md-4">
                    <img src="http://localhost/cdicconference.com/assets/images/QueenSirikit.png" 
                         class="img-fluid demo-image" 
                         alt="Queen Sirikit"
                         onclick="ImagePopup.open('http://localhost/cdicconference.com/assets/images/QueenSirikit.png', 'Queen Sirikit National Convention Centre')"
                         onerror="this.src='https://via.placeholder.com/300x200/007bff/ffffff?text=Queen+Sirikit+Image'">
                </div>
                <div class="col-md-8">
                    <div class="code-example">
&lt;img src="your-image.jpg" 
     onclick="ImagePopup.open('your-image.jpg', 'Image Caption')"
     style="cursor: pointer;"&gt;
                    </div>
                </div>
            </div>
        </div>

        <!-- Method 2: Data attributes -->
        <div class="feature-box">
            <h3 class="text-primary">Method 2: Data attributes (Auto-init)</h3>
            <p>Add <code>data-popup</code> attribute to automatically enable popup:</p>
            
            <div class="row">
                <div class="col-md-4">
                    <img src="https://via.placeholder.com/300x200/28a745/ffffff?text=CDIC+Conference" 
                         class="img-fluid demo-image" 
                         alt="CDIC Conference"
                         data-popup="CDIC Conference 2024 - Cybersecurity Excellence">
                </div>
                <div class="col-md-8">
                    <div class="code-example">
&lt;img src="your-image.jpg" 
     data-popup="Your image caption here"
     alt="Image description"&gt;
                    </div>
                    <small class="text-muted">Images with data-popup attribute are automatically clickable</small>
                </div>
            </div>
        </div>

        <!-- Method 3: Gallery -->
        <div class="feature-box">
            <h3 class="text-primary">Method 3: Image Gallery</h3>
            <p>Group images together for navigation with <code>data-gallery</code>:</p>
            
            <div class="row">
                <div class="col-md-3">
                    <img src="https://via.placeholder.com/250x200/007bff/ffffff?text=Image+1" 
                         class="img-fluid demo-image mb-2" 
                         data-gallery="demo-gallery"
                         data-caption="First image in gallery">
                </div>
                <div class="col-md-3">
                    <img src="https://via.placeholder.com/250x200/28a745/ffffff?text=Image+2" 
                         class="img-fluid demo-image mb-2" 
                         data-gallery="demo-gallery"
                         data-caption="Second image in gallery">
                </div>
                <div class="col-md-3">
                    <img src="https://via.placeholder.com/250x200/dc3545/ffffff?text=Image+3" 
                         class="img-fluid demo-image mb-2" 
                         data-gallery="demo-gallery"
                         data-caption="Third image in gallery">
                </div>
                <div class="col-md-3">
                    <img src="http://localhost/cdicconference.com/assets/images/QueenSirikit.png" 
                         class="img-fluid demo-image mb-2" 
                         data-gallery="demo-gallery"
                         data-caption="Queen Sirikit Centre"
                         onerror="this.src='https://via.placeholder.com/250x200/ffc107/000000?text=Image+4'">
                </div>
            </div>
            
            <div class="code-example">
&lt;img src="image1.jpg" data-gallery="my-gallery" data-caption="First image"&gt;
&lt;img src="image2.jpg" data-gallery="my-gallery" data-caption="Second image"&gt;
&lt;img src="image3.jpg" data-gallery="my-gallery" data-caption="Third image"&gt;

&lt;script&gt;
ImagePopup.initGallery('my-gallery');
&lt;/script&gt;
            </div>
        </div>

        <!-- Method 4: Programmatic -->
        <div class="feature-box">
            <h3 class="text-primary">Method 4: Programmatic Control</h3>
            <p>Open popups programmatically with JavaScript:</p>
            
            <div class="text-center">
                <button class="btn btn-primary btn-demo" onclick="openQueenSirikitImage()">
                    Open Queen Sirikit Image
                </button>
                <button class="btn btn-success btn-demo" onclick="openCustomImage()">
                    Open Custom URL
                </button>
                <button class="btn btn-info btn-demo" onclick="openImageArray()">
                    Open Image Array
                </button>
            </div>
            
            <div class="code-example">
// Open single image
ImagePopup.open('image.jpg', 'Caption');

// Open with custom URL
function openCustomImage() {
    const url = prompt('Enter image URL:');
    if (url) ImagePopup.open(url, 'Custom Image');
}

// Open array of images
const images = [
    {src: 'img1.jpg', caption: 'Image 1'},
    {src: 'img2.jpg', caption: 'Image 2'}
];
ImagePopup.open('img1.jpg', 'Image 1', images);
            </div>
        </div>

        <!-- Integration Instructions -->
        <div class="feature-box">
            <h3 class="text-primary">Integration Instructions</h3>
            <p>To add this popup functionality to your existing pages:</p>
            
            <ol>
                <li><strong>Include the CSS file:</strong>
                    <div class="code-example">&lt;link rel="stylesheet" href="assets/css/image-popup.css"&gt;</div>
                </li>
                
                <li><strong>Include the JavaScript file:</strong>
                    <div class="code-example">&lt;script src="assets/js/image-popup.js"&gt;&lt;/script&gt;</div>
                </li>
                
                <li><strong>Add popup to images using any method above</strong></li>
            </ol>
            
            <h4 class="text-primary mt-4">Features:</h4>
            <ul>
                <li>✅ Responsive design</li>
                <li>✅ Keyboard navigation (ESC, Arrow keys)</li>
                <li>✅ Touch/swipe support</li>
                <li>✅ Loading indicators</li>
                <li>✅ Image counter for galleries</li>
                <li>✅ Error handling for broken images</li>
                <li>✅ Accessibility support</li>
                <li>✅ No dependencies (pure JavaScript)</li>
            </ul>
        </div>
    </div>

    <!-- Include Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Include the popup JavaScript -->
    <script src="assets/js/image-popup.js"></script>
    
    <script>
        // Initialize gallery
        ImagePopup.initGallery('demo-gallery');
        
        // Demo functions
        function openQueenSirikitImage() {
            ImagePopup.open(
                'http://localhost/cdicconference.com/assets/images/QueenSirikit.png',
                'Queen Sirikit National Convention Centre - CDIC Conference Venue'
            );
        }
        
        function openCustomImage() {
            const url = prompt('Enter image URL:', 'http://localhost/cdicconference.com/assets/images/QueenSirikit.png');
            if (url) {
                ImagePopup.open(url, 'Custom Image');
            }
        }
        
        function openImageArray() {
            const images = [
                {
                    src: 'http://localhost/cdicconference.com/assets/images/QueenSirikit.png',
                    caption: 'Queen Sirikit National Convention Centre'
                },
                {
                    src: 'https://via.placeholder.com/800x600/007bff/ffffff?text=CDIC+Conference+2024',
                    caption: 'CDIC Conference 2024'
                },
                {
                    src: 'https://via.placeholder.com/800x600/28a745/ffffff?text=Cybersecurity+Summit',
                    caption: 'Cybersecurity Summit'
                },
                {
                    src: 'https://via.placeholder.com/800x600/dc3545/ffffff?text=Security+Workshop',
                    caption: 'Security Workshop'
                }
            ];
            
            ImagePopup.open(images[0].src, images[0].caption, images);
        }
    </script>
</body>
</html>
