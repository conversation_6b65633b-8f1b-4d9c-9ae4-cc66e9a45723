<?php
/**
 * Mailchimp Transactional Email Sender
 * 
 * This file provides functionality to send emails using Mailchimp Transactional API
 * API Key: *************************************
 */

require_once __DIR__ . '/lib/Configuration.php';

use MailchimpTransactional\Configuration;

class MailSender {
    private $config;
    private $apiKey = '*************************************';
    
    public function __construct() {
        $this->config = new Configuration();
        $this->config->setApiKey($this->apiKey);
    }
    
    /**
     * Send a simple email
     * 
     * @param string $to_email Recipient email address
     * @param string $to_name Recipient name
     * @param string $from_email Sender email address
     * @param string $from_name Sender name
     * @param string $subject Email subject
     * @param string $html_content HTML content of the email
     * @param string $text_content Plain text content of the email (optional)
     * @return mixed Response from Mailchimp API
     */
    public function sendEmail($to_email, $to_name, $from_email, $from_name, $subject, $html_content, $text_content = '') {
        $message = [
            'html' => $html_content,
            'text' => $text_content,
            'subject' => $subject,
            'from_email' => $from_email,
            'from_name' => $from_name,
            'to' => [
                [
                    'email' => $to_email,
                    'name' => $to_name,
                    'type' => 'to'
                ]
            ],
            'headers' => [
                'Reply-To' => $from_email
            ],
            'important' => false,
            'track_opens' => true,
            'track_clicks' => true,
            'auto_text' => true,
            'auto_html' => false,
            'inline_css' => true,
            'url_strip_qs' => false,
            'preserve_recipients' => false,
            'view_content_link' => false,
            'tracking_domain' => null,
            'signing_domain' => null,
            'return_path_domain' => null
        ];
        
        $body = [
            'message' => $message,
            'async' => false,
            'ip_pool' => 'Main Pool'
        ];
        
        try {
            $response = $this->config->messages->send($body);
            return $response;
        } catch (Exception $e) {
            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Send email with attachments
     * 
     * @param string $to_email Recipient email address
     * @param string $to_name Recipient name
     * @param string $from_email Sender email address
     * @param string $from_name Sender name
     * @param string $subject Email subject
     * @param string $html_content HTML content of the email
     * @param array $attachments Array of attachments with 'type', 'name', and 'content' keys
     * @param string $text_content Plain text content of the email (optional)
     * @return mixed Response from Mailchimp API
     */
    public function sendEmailWithAttachments($to_email, $to_name, $from_email, $from_name, $subject, $html_content, $attachments = [], $text_content = '') {
        $message = [
            'html' => $html_content,
            'text' => $text_content,
            'subject' => $subject,
            'from_email' => $from_email,
            'from_name' => $from_name,
            'to' => [
                [
                    'email' => $to_email,
                    'name' => $to_name,
                    'type' => 'to'
                ]
            ],
            'headers' => [
                'Reply-To' => $from_email
            ],
            'important' => false,
            'track_opens' => true,
            'track_clicks' => true,
            'auto_text' => true,
            'auto_html' => false,
            'inline_css' => true,
            'url_strip_qs' => false,
            'preserve_recipients' => false,
            'view_content_link' => false,
            'tracking_domain' => null,
            'signing_domain' => null,
            'return_path_domain' => null,
            'attachments' => $attachments
        ];
        
        $body = [
            'message' => $message,
            'async' => false,
            'ip_pool' => 'Main Pool'
        ];
        
        try {
            $response = $this->config->messages->send($body);
            return $response;
        } catch (Exception $e) {
            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Send email to multiple recipients
     * 
     * @param array $recipients Array of recipients with 'email' and 'name' keys
     * @param string $from_email Sender email address
     * @param string $from_name Sender name
     * @param string $subject Email subject
     * @param string $html_content HTML content of the email
     * @param string $text_content Plain text content of the email (optional)
     * @return mixed Response from Mailchimp API
     */
    public function sendBulkEmail($recipients, $from_email, $from_name, $subject, $html_content, $text_content = '') {
        $to_array = [];
        foreach ($recipients as $recipient) {
            $to_array[] = [
                'email' => $recipient['email'],
                'name' => $recipient['name'],
                'type' => 'to'
            ];
        }
        
        $message = [
            'html' => $html_content,
            'text' => $text_content,
            'subject' => $subject,
            'from_email' => $from_email,
            'from_name' => $from_name,
            'to' => $to_array,
            'headers' => [
                'Reply-To' => $from_email
            ],
            'important' => false,
            'track_opens' => true,
            'track_clicks' => true,
            'auto_text' => true,
            'auto_html' => false,
            'inline_css' => true,
            'url_strip_qs' => false,
            'preserve_recipients' => false,
            'view_content_link' => false,
            'tracking_domain' => null,
            'signing_domain' => null,
            'return_path_domain' => null
        ];
        
        $body = [
            'message' => $message,
            'async' => false,
            'ip_pool' => 'Main Pool'
        ];
        
        try {
            $response = $this->config->messages->send($body);
            return $response;
        } catch (Exception $e) {
            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get account information
     * 
     * @return mixed Response from Mailchimp API
     */
    public function getAccountInfo() {
        try {
            $response = $this->config->users->info();
            return $response;
        } catch (Exception $e) {
            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }
}

// Example usage functions
function sendTestEmail() {
    $mailer = new MailSender();
    
    $result = $mailer->sendEmail(
        '<EMAIL>',           // To email
        'Recipient Name',                  // To name
        '<EMAIL>',       // From email
        'CDIC Conference',                 // From name
        'Test Email from CDIC Conference', // Subject
        '<h1>Hello!</h1><p>This is a test email from CDIC Conference.</p>', // HTML content
        'Hello! This is a test email from CDIC Conference.' // Text content
    );
    
    return $result;
}

function sendWelcomeEmail($to_email, $to_name) {
    $mailer = new MailSender();
    
    $html_content = '
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; }
            .header { background-color: #f8f9fa; padding: 20px; text-align: center; }
            .content { padding: 20px; }
            .footer { background-color: #f8f9fa; padding: 10px; text-align: center; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Welcome to CDIC Conference!</h1>
        </div>
        <div class="content">
            <p>Dear ' . htmlspecialchars($to_name) . ',</p>
            <p>Thank you for your interest in the CDIC Conference. We are excited to have you join us!</p>
            <p>Stay tuned for more updates about the conference schedule, speakers, and registration details.</p>
            <p>Best regards,<br>CDIC Conference Team</p>
        </div>
        <div class="footer">
            <p>&copy; 2024 CDIC Conference. All rights reserved.</p>
        </div>
    </body>
    </html>';
    
    $text_content = "Dear $to_name,\n\nThank you for your interest in the CDIC Conference. We are excited to have you join us!\n\nStay tuned for more updates about the conference schedule, speakers, and registration details.\n\nBest regards,\nCDIC Conference Team\n\n© 2024 CDIC Conference. All rights reserved.";
    
    $result = $mailer->sendEmail(
        $to_email,
        $to_name,
        '<EMAIL>',
        'CDIC Conference',
        'Welcome to CDIC Conference!',
        $html_content,
        $text_content
    );
    
    return $result;
}

// If this file is called directly, you can test the functionality
if (basename(__FILE__) == basename($_SERVER["SCRIPT_FILENAME"])) {
    // Uncomment the line below to test sending an email
    // $result = sendTestEmail();
    // echo json_encode($result, JSON_PRETTY_PRINT);
    
    echo "Mail sender initialized successfully!\n";
    echo "Use the MailSender class or the helper functions to send emails.\n";
}
?>
