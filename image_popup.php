<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Popup - CDIC Conference</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }

        h1 {
            color: #007bff;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }

        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .image-item {
            position: relative;
            cursor: pointer;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .image-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .image-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            display: block;
        }

        .image-item .overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .image-item:hover .overlay {
            opacity: 1;
        }

        .overlay i {
            color: white;
            font-size: 2rem;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
            animation: fadeIn 0.3s ease;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .modal-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
            animation: zoomIn 0.3s ease;
        }

        @keyframes zoomIn {
            from { transform: scale(0.5); }
            to { transform: scale(1); }
        }

        .modal-image {
            width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }

        .close {
            position: absolute;
            top: -40px;
            right: 0;
            color: white;
            font-size: 35px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #007bff;
        }

        .modal-caption {
            color: white;
            text-align: center;
            margin-top: 15px;
            font-size: 1.2rem;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
        }

        /* Navigation arrows */
        .nav-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            color: white;
            font-size: 3rem;
            cursor: pointer;
            user-select: none;
            transition: color 0.3s ease;
            background: rgba(0,0,0,0.5);
            padding: 10px 15px;
            border-radius: 50%;
        }

        .nav-arrow:hover {
            color: #007bff;
            background: rgba(0,0,0,0.8);
        }

        .prev {
            left: 20px;
        }

        .next {
            right: 20px;
        }

        /* Demo button */
        .demo-btn {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1rem;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }

        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
        }

        .url-input {
            width: 100%;
            max-width: 500px;
            padding: 12px;
            margin: 10px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 1rem;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .url-input:focus {
            border-color: #007bff;
        }

        .info-box {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
            text-align: left;
        }

        .info-box h3 {
            color: #007bff;
            margin-bottom: 10px;
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }

        @media (max-width: 768px) {
            .nav-arrow {
                font-size: 2rem;
                padding: 8px 12px;
            }
            
            .prev {
                left: 10px;
            }
            
            .next {
                right: 10px;
            }
            
            h1 {
                font-size: 2rem;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-images"></i> Image Popup Gallery</h1>
        
        <div class="info-box">
            <h3><i class="fas fa-info-circle"></i> How to Use</h3>
            <p>Click on any image below to view it in a popup modal. You can also enter a custom image URL to test the popup functionality.</p>
        </div>

        <!-- Custom URL Input -->
        <div style="margin: 30px 0;">
            <input type="text" id="customUrl" class="url-input" 
                   placeholder="Enter image URL (e.g., http://localhost/cdicconference.com/assets/images/QueenSirikit.png)"
                   value="http://localhost/cdicconference.com/assets/images/QueenSirikit.png">
            <br>
            <button class="demo-btn" onclick="openCustomImage()">
                <i class="fas fa-external-link-alt"></i> Open Custom Image
            </button>
        </div>

        <!-- Sample Image Gallery -->
        <div class="image-gallery">
            <div class="image-item" onclick="openModal('http://localhost/cdicconference.com/assets/images/QueenSirikit.png', 'Queen Sirikit Image')">
                <img src="http://localhost/cdicconference.com/assets/images/QueenSirikit.png" alt="Queen Sirikit" onerror="this.src='https://via.placeholder.com/300x200?text=Image+Not+Found'">
                <div class="overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <!-- Demo images with placeholder fallbacks -->
            <div class="image-item" onclick="openModal('https://via.placeholder.com/800x600/007bff/ffffff?text=CDIC+Conference+2024', 'CDIC Conference 2024')">
                <img src="https://via.placeholder.com/300x200/007bff/ffffff?text=CDIC+2024" alt="CDIC 2024">
                <div class="overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <div class="image-item" onclick="openModal('https://via.placeholder.com/800x600/28a745/ffffff?text=Cybersecurity+Summit', 'Cybersecurity Summit')">
                <img src="https://via.placeholder.com/300x200/28a745/ffffff?text=Cyber+Summit" alt="Cyber Summit">
                <div class="overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
            
            <div class="image-item" onclick="openModal('https://via.placeholder.com/800x600/dc3545/ffffff?text=Security+Workshop', 'Security Workshop')">
                <img src="https://via.placeholder.com/300x200/dc3545/ffffff?text=Workshop" alt="Workshop">
                <div class="overlay">
                    <i class="fas fa-search-plus"></i>
                </div>
            </div>
        </div>

        <div class="info-box">
            <h3><i class="fas fa-code"></i> Implementation Code</h3>
            <p>Here's how to implement the popup functionality in your own pages:</p>
            <div class="code-block">
&lt;!-- HTML --&gt;
&lt;img src="your-image.jpg" onclick="openModal('your-image.jpg', 'Image Title')" style="cursor: pointer;"&gt;

&lt;!-- JavaScript --&gt;
&lt;script&gt;
function openModal(imageSrc, caption) {
    document.getElementById('modalImage').src = imageSrc;
    document.getElementById('modalCaption').textContent = caption;
    document.getElementById('imageModal').classList.add('show');
}
&lt;/script&gt;
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div id="imageModal" class="modal" onclick="closeModal()">
        <div class="modal-content" onclick="event.stopPropagation()">
            <span class="close" onclick="closeModal()">&times;</span>
            <span class="nav-arrow prev" onclick="previousImage()">&#10094;</span>
            <span class="nav-arrow next" onclick="nextImage()">&#10095;</span>
            <img id="modalImage" class="modal-image" src="" alt="Modal Image">
            <div id="modalCaption" class="modal-caption"></div>
        </div>
    </div>

    <script>
        let currentImages = [];
        let currentIndex = 0;

        function openModal(imageSrc, caption) {
            // Store current image info
            currentImages = [
                {
                    src: 'http://localhost/cdicconference.com/assets/images/QueenSirikit.png',
                    caption: 'Queen Sirikit Image'
                },
                {
                    src: 'https://via.placeholder.com/800x600/007bff/ffffff?text=CDIC+Conference+2024',
                    caption: 'CDIC Conference 2024'
                },
                {
                    src: 'https://via.placeholder.com/800x600/28a745/ffffff?text=Cybersecurity+Summit',
                    caption: 'Cybersecurity Summit'
                },
                {
                    src: 'https://via.placeholder.com/800x600/dc3545/ffffff?text=Security+Workshop',
                    caption: 'Security Workshop'
                }
            ];

            // Find current index
            currentIndex = currentImages.findIndex(img => img.src === imageSrc);
            if (currentIndex === -1) {
                currentIndex = 0;
                currentImages.unshift({src: imageSrc, caption: caption});
            }

            showCurrentImage();
            document.getElementById('imageModal').classList.add('show');
        }

        function showCurrentImage() {
            const currentImage = currentImages[currentIndex];
            document.getElementById('modalImage').src = currentImage.src;
            document.getElementById('modalCaption').textContent = currentImage.caption;
        }

        function closeModal() {
            document.getElementById('imageModal').classList.remove('show');
        }

        function nextImage() {
            currentIndex = (currentIndex + 1) % currentImages.length;
            showCurrentImage();
        }

        function previousImage() {
            currentIndex = (currentIndex - 1 + currentImages.length) % currentImages.length;
            showCurrentImage();
        }

        function openCustomImage() {
            const url = document.getElementById('customUrl').value.trim();
            if (url) {
                openModal(url, 'Custom Image');
            } else {
                alert('Please enter a valid image URL');
            }
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (document.getElementById('imageModal').classList.contains('show')) {
                switch(e.key) {
                    case 'Escape':
                        closeModal();
                        break;
                    case 'ArrowLeft':
                        previousImage();
                        break;
                    case 'ArrowRight':
                        nextImage();
                        break;
                }
            }
        });

        // Prevent modal from closing when clicking on the image
        document.getElementById('modalImage').addEventListener('click', function(e) {
            e.stopPropagation();
        });
    </script>
</body>
</html>
